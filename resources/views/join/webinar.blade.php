<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $webinar->title }} - Webinar</title>

@php
    // Kiểm tra lại một lần nữa nếu webinar đã kết thúc và không cho phép xem lại
    if ($webinarEnded && !$webinar->allow_replay) {
        // Redirect back to the join page using JavaScript for immediate effect
        echo '<script>
            alert("Buổi webinar đã kết thúc và không được phép xem lại.");
            window.location.href = "'.route('join.show', ['code' => $webinar->join_code]).'";
        </script>';
        exit;
    }
@endphp


@php
    $isLiveLearning = isset($is_live_learning) && $is_live_learning==1;
    $isLiveReal = isset($is_live_stream) && $is_live_stream==1;
    $isLiveSimulate = isset($isLive) && $isLive && !$isLiveReal;
    $isLiveStream = $isLiveReal || $isLiveSimulate;
@endphp

<!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Video.js -->
    <link href="https://vjs.zencdn.net/8.3.0/video-js.css" rel="stylesheet"/>

    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.12.1/font/bootstrap-icons.min.css" rel="stylesheet"/>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Vite Assets -->
    @vite(['resources/css/webinar.css'])

    <style>
        /* Comment button loading state styles */
        .btn-loading .fa-spin {
            animation: fa-spin 1s infinite linear;
        }

        @keyframes fa-spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .d-none {
            display: none;
        }

        #comment-submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    </style>

</head>
<body>
<div class="container-fluid webinar-container">
    <!-- Mobile Audio Permission Popup -->
    <div id="mobile-audio-popup" class="mobile-audio-popup" style="display: none;">
        <div class="mobile-audio-popup-content">
            <div class="mobile-audio-icon">
                <i class="fas fa-volume-up"></i>
            </div>
            <h3>Cho phép âm thanh</h3>
            <p>Nhấn để bật âm thanh cho webinar</p>
            <div class="mobile-audio-buttons">
                <button id="enable-audio-btn" class="btn-enable-audio">
                    <i class="fas fa-volume-up"></i>
                    Bật âm thanh
                </button>
            </div>
        </div>
    </div>
    <div class="main-content" id="main-content">
        <div class="video-section">

            <div class="video-container">
            @if((isset($is_live_stream) && $is_live_stream==1) || (isset($isLiveLearning) && $isLiveLearning==1))
                @switch($type_live)
                    @case('youtube')
                    <!-- YouTube Player API -->
                        <div id="native-live-video" style="width: 100%; height: 100%;"></div>

                        <script>
                            // Load YouTube Player API
                            var tag = document.createElement('script');
                            tag.src = "https://www.youtube.com/iframe_api";
                            var firstScriptTag = document.getElementsByTagName('script')[0];
                            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

                            var youtubePlayer;
                            var youtubeVideoId = '{{$link_live}}'.split('/').pop().split('?')[0];

                            // Extract video ID from various YouTube URL formats
                            if (youtubeVideoId.includes('embed/')) {
                                youtubeVideoId = youtubeVideoId.replace('embed/', '');
                            }
                            if (youtubeVideoId.includes('watch?v=')) {
                                youtubeVideoId = youtubeVideoId.split('watch?v=')[1].split('&')[0];
                            }

                            function onYouTubeIframeAPIReady() {
                                youtubePlayer = new YT.Player('native-live-video', {
                                    height: '100%',
                                    width: '100%',
                                    videoId: youtubeVideoId,
                                    playerVars: {
                                        'autoplay': 1,
                                        'mute': 1,
                                        'controls': 1,
                                        'rel': 0,
                                        'modestbranding': 1
                                    },
                                    events: {
                                        'onReady': onPlayerReady,
                                        'onStateChange': onPlayerStateChange
                                    }
                                });
                            }

                            function onPlayerReady(event) {
                                console.log('YouTube player ready');
                                // Start tracking time
                                setInterval(function () {
                                    if (youtubePlayer && youtubePlayer.getCurrentTime) {
                                        var currentTime = youtubePlayer.getCurrentTime();
                                        var duration = youtubePlayer.getDuration();

                                        // console.log('YouTube current time:', currentTime);
                                        // console.log('YouTube duration:', duration);

                                        // Update progress display
                                        if (document.getElementById('video-progress')) {
                                            document.getElementById('video-progress').textContent = formatTimeWithHours(currentTime);
                                        }

                                        // Trigger any callbacks that need the timestamp
                                        if (typeof window.onYouTubeTimeUpdate === 'function') {
                                            window.onYouTubeTimeUpdate(currentTime);
                                        }
                                    }
                                }, 1000);
                            }

                            function onPlayerStateChange(event) {
                                console.log('YouTube player state changed:', event.data);
                                // YT.PlayerState.PLAYING = 1
                                // YT.PlayerState.PAUSED = 2
                                // YT.PlayerState.ENDED = 0
                            }

                            // Make player accessible globally
                            window.youtubePlayer = youtubePlayer;
                        </script>
                        @break
                        @case('facebook')
                        <iframe id="native-live-video"
                                src="https://www.facebook.com/plugins/video.php?href={{$link_live}}"
                                style="border: none; position: absolute; top: 0; left: 0; height: 100%; width: 100%;pointer-events: none;"
                                allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
                                sandbox="allow-scripts allow-same-origin allow-presentation"
                                allowfullscreen="true"
                        ></iframe>
                        @break
                        @case('m3u8')
                        <script src="https://cdn.jwplayer.com/libraries/IDzF9Zmk.js"></script>

                        <div id="native-live-video"></div>

                        <script>
                            jwplayer("native-live-video").setup({
                                file: "{{$link_live}}",
                                type: "hls",
                                width: "100%",
                                aspectratio: "16:9",
                                autostart: true,
                                controls: true
                            });
                        </script>
                        @break
                        @case('jitsi')
                        @php
                            $parsed = parse_url($link_live);

                            $domain_jitsi = $parsed['host'];
                            $path_jitsi = ltrim($parsed['path'], '/');
                        @endphp
                        <div id="jitsi-container" class="native-live-video" style="width:100%; height:100%;"></div>

                        <script src='https://zoom.topid.vn/external_api.js'></script>
                        <script>
                            let isLobbyEnabled = true;
                            let waitingParticipants = [];

                            // Jitsi configuration
                            const domain = "{{$domain_jitsi}}";
                            const options = {
                                roomName: "{{$path_jitsi}}",
                                password: 'quyenanh',
                                displayName: '{{ $participant->name ?? 'Khách' }}',

                                width: '100%',
                                height: '100%',
                                parentNode: document.querySelector('#jitsi-container'),

                                configOverwrite: {
                                    prejoinPageEnabled: true,
                                    enableWelcomePage: false,
                                    enableClosePage: false,
                                    defaultLanguage: 'vi',
                                    enableLobby: true,
                                    enableLobbyChat: true,
                                    disableLobby: false,
                                    requireDisplayName: false,
                                    startWithAudioMuted: false,
                                    startWithVideoMuted: false,
                                    enableUserRolesBasedOnToken: false,
                                    disableDeepLinking: true,
                                    hideDisplayName: false,
                                    autoEnableLobby: true,
                                    lobbyThreshold: 1
                                },

                                interfaceConfigOverwrite: {
                                    TOOLBAR_BUTTONS: [
                                        'microphone', 'camera', 'desktop', 'hangup', 'chat', 'security'
                                    ],
                                    SHOW_JITSI_WATERMARK: false,
                                    SHOW_WATERMARK_FOR_GUESTS: false,
                                    DEFAULT_BACKGROUND: '#040404',
                                    HIDE_INVITE_MORE_HEADER: true,
                                    DISABLE_JOIN_LEAVE_NOTIFICATIONS: false,
                                    DISABLE_PRESENCE_STATUS: false,
                                    SETTINGS_SECTIONS: ['devices', 'language', 'moderator', 'profile', 'calendar'],
                                    SHOW_CHROME_EXTENSION_BANNER: false
                                },

                                userInfo: {
                                    displayName: '{{ $participant->name ?? 'Khách' }}'
                                }
                            };
                            const api = new JitsiMeetExternalAPI(domain, options);
                            api.addEventListener('videoConferenceJoined', () => {
                                api.executeCommand('toggleAudio');
                                api.executeCommand('toggleVideo');
                            });

                        </script>
                        @break
                        @default
                        <iframe id="native-live-video"
                                src="{{$link_live}}"
                                style="border: none; position: absolute; top: 0; left: 0; height: 100%; width: 100%;"
                                allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
                                allowfullscreen="true"
                        ></iframe>
                    @endswitch

                @elseif($webinar->video_path && isset($isLive) && $isLive)
                    <div class="position-relative" style="height: 100%; width: 100%;">
                        <div class="live-indicator">
                            <i class="fas fa-circle"></i> LIVE
                        </div>
                        <video
                            id="native-live-video"
                            class="native-video-player"
                            autoplay
                            muted
                            playsinline
                            data-duration="{{ $webinar->video_duration_minutes ?? 120 }}"
                            style="width:100%; height:100%; background:#000; object-fit:contain;"
                            poster="{{ asset('images/video-poster.svg') }}">
                            @if($webinar->s3_url)
                                @php
                                    $s3Url = $webinar->s3_url;

                                @endphp
                                <source src="{{ $s3Url }}" type="video/mp4">
                            @elseif(strpos($webinar->video_path, 's3://') === 0)
                            @else
                                <source src="{{ Storage::url(str_replace('public/', '', $webinar->video_path)) }}"
                                        type="video/mp4">
                            @endif
                            Trình duyệt của bạn không hỗ trợ thẻ video.
                        </video>

                        <div id="webinar-ended-overlay"
                             style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 100; flex-direction: column; align-items: center; justify-content: center; text-align: center; padding: 20px;">
                            <i class="fas fa-check-circle"
                               style="font-size: 3rem; color: #4cc9f0; margin-bottom: 20px;"></i>
                            <h2 style="font-size: 1.75rem; margin-bottom: 15px; color: white;">Buổi webinar đã kết
                                thúc</h2>
                            <p style="font-size: 1rem; color: #8b93a7; margin-bottom: 20px;">Cảm ơn bạn đã tham gia cùng
                                chúng tôi.</p>
                        </div>

                        <div id="volume-control" class="volume-control">
                            <div class="volume-notification">
                                <i class="fas fa-volume-mute"></i>
                                <span class="volume-text">Âm thanh đang tắt. Nhấn để bật âm thanh</span>
                            </div>
                        </div>


                        <div class="stream-duration" id="stream-duration-container" style="display: none;">
                            <i class="fas fa-broadcast-tower"></i> Đang phát: <span id="stream-duration">00:00:00</span>
                        </div>

                        <div id="debug-info" class="debug-info">
                            Video timestamp: <span id="debug-timestamp">0</span>s<br>
                            Elapsed time: <span id="debug-elapsed">0</span>s<br>
                            Stream start: <span id="debug-start">{{$webinar->start_date ?? 'N/A'}}</span><br>
                            Server timestamp: {{$videoTimestamp ?? 0}}s<br>
                            Current time: {{date('Y-m-d H:i:s')}}
                        </div>
                    </div>

                @elseif(isset($isLive) && $isLive)

                    <div class="position-relative" style="height: 100%; width: 100%;">
                        <div id="loading-container-live"
                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, #141e30 0%, #243b55 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 10;">
                            <div style="text-align: center; width: 100%; max-width: 500px; padding: 30px;">
                                <div style="width: 100px; height: 100px; margin: 0 auto 30px; position: relative;">
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 100px; height: 100px; position: absolute; top: 0; left: 0; opacity: 0.2; animation: spinner-grow 2s ease-in-out infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 80px; height: 80px; position: absolute; top: 10px; left: 10px; opacity: 0.4; animation: spinner-grow 2s ease-in-out 0.3s infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 60px; height: 60px; position: absolute; top: 20px; left: 20px; opacity: 0.6; animation: spinner-grow 2s ease-in-out 0.6s infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 40px; height: 40px; position: absolute; top: 30px; left: 30px; opacity: 0.8; animation: spinner-grow 2s ease-in-out 0.9s infinite alternate;"></div>
                                </div>
                                <h2 style="font-size: 35px; margin-bottom: 1.5rem; color: white; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                                    Đang tham gia buổi webinar</h2>
                                <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); margin-bottom: 20px; line-height: 1.6;">
                                    Vui lòng đợi trong khi chúng tôi đang kết nối bạn đến buổi webinar...</p>
                                <div
                                    style="width: 100%; max-width: 300px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px; margin: 15px auto; overflow: hidden;">
                                    <div
                                        style="height: 100%; width: 30%; background: var(--primary-color); border-radius: 3px; animation: progress 2s ease-in-out infinite;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                @elseif(isset($inWaitingPeriod) && $inWaitingPeriod)

                    <div class="position-relative" style="height: 100%; width: 100%;">
                        <div id="loading-container-waiting"
                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, #141e30 0%, #243b55 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 10;">
                            <div style="text-align: center; width: 100%; max-width: 500px; padding: 30px;">
                                <div style="width: 100px; height: 100px; margin: 0 auto 30px; position: relative;">
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 100px; height: 100px; position: absolute; top: 0; left: 0; opacity: 0.2; animation: spinner-grow 2s ease-in-out infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 80px; height: 80px; position: absolute; top: 10px; left: 10px; opacity: 0.4; animation: spinner-grow 2s ease-in-out 0.3s infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 60px; height: 60px; position: absolute; top: 20px; left: 20px; opacity: 0.6; animation: spinner-grow 2s ease-in-out 0.6s infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 40px; height: 40px; position: absolute; top: 30px; left: 30px; opacity: 0.8; animation: spinner-grow 2s ease-in-out 0.9s infinite alternate;"></div>
                                </div>
                                <h2 style="font-size: 35px; margin-bottom: 1.5rem; color: white; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                                    Đang tham gia buổi webinar</h2>
                                <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); margin-bottom: 20px; line-height: 1.6;">
                                    Buổi webinar sẽ bắt đầu sau:</p>

                                <div
                                    style="margin: 20px 0; background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
                                    <div id="countdown-display" class="countdown-value">
                                        <span id="hours"
                                              style="font-size: 3rem; color: var(--primary-color); font-weight: 700; text-shadow: 0 2px 10px rgba(61, 90, 241, 0.5);">00</span>
                                        <span style="font-size: 3rem; color: white; margin: 0 5px;">:</span>
                                        <span id="minutes"
                                              style="font-size: 3rem; color: var(--primary-color); font-weight: 700; text-shadow: 0 2px 10px rgba(61, 90, 241, 0.5);">00</span>
                                        <span style="font-size: 3rem; color: white; margin: 0 5px;">:</span>
                                        <span id="seconds"
                                              style="font-size: 3rem; color: var(--primary-color); font-weight: 700; text-shadow: 0 2px 10px rgba(61, 90, 241, 0.5);">00</span>
                                    </div>
                                    <div style="font-size: 1rem; color: rgba(255,255,255,0.7); margin-top: 10px;">
                                        Bắt đầu lúc: {{ $formattedStartTime ?? '(Không có)' }}
                                    </div>
                                </div>

                                <div
                                    style="width: 100%; max-width: 300px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px; margin: 15px auto; overflow: hidden;">
                                    <div
                                        style="height: 100%; width: 30%; background: var(--primary-color); border-radius: 3px; animation: progress 2s ease-in-out infinite;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                @elseif(isset($webinarEnded) && $webinarEnded)

                    @if($webinar->allow_replay)
                        <div class="position-relative" style="height: 100%; width: 100%;">
                            <video
                                id="native-video"
                                class="native-video-player"
                                controls
                                autoplay
                                playsinline
                                style="width:100%; height:100%; background:#000; object-fit:contain;"
                                {{--                                poster="{{ asset('images/video-poster.svg') }}"--}}
                            >
                                @if($webinar->s3_url)
                                    @php
                                        // Fix S3 URL if it doesn't contain the bucket name
                                        $s3Url = $webinar->s3_url;
                                        //$bucket = config('filesystems.disks.vns3.bucket');
                                        // Check if URL already contains bucket name
                                       // if (!str_contains($s3Url, '/' . $bucket . '/') && str_contains($s3Url, '/webinar-')) {
                                            // Insert bucket name into URL
                                        //    $baseUrl = substr($s3Url, 0, strpos($s3Url, '/webinar-'));
                                        //    $path = substr($s3Url, strpos($s3Url, '/webinar-'));
                                       //     $s3Url = $baseUrl . '/' . $bucket . $path;
                                       // }
                                    @endphp
                                    <source src="{{ $s3Url }}" type="video/mp4">
                                @elseif(strpos($webinar->video_path, 's3://') === 0)
                                    Skip if s3 path but no URL
                                @else
                                    <source src="{{ Storage::url(str_replace('public/', '', $webinar->video_path)) }}"
                                            type="video/mp4">
                                @endif
                                Trình duyệt của bạn không hỗ trợ thẻ video.
                            </video>
                            <!-- Webinar ended overlay for regular video -->
                            <div id="webinar-ended-overlay-regular"
                                 style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 100; flex-direction: column; align-items: center; justify-content: center; text-align: center; padding: 20px;">
                                <i class="fas fa-check-circle"
                                   style="font-size: 3rem; color: #4cc9f0; margin-bottom: 20px;"></i>
                                <h2 style="font-size: 1.75rem; margin-bottom: 15px; color: white;">Buổi webinar đã kết
                                    thúc</h2>
                                <p style="font-size: 1rem; color: #8b93a7; margin-bottom: 20px;">Cảm ơn bạn đã tham gia
                                    cùng chúng tôi.</p>
                            </div>
                            <!-- Time display for recorded video -->
                            <div class="time-display">
                                <span id="current-time-recording">00:00</span> / <span
                                    id="total-time-recording">00:00</span>
                            </div>
                        </div>

                    @else
                        <div class="position-relative" style="height: 100%; width: 100%;">
                            <div id="loading-container-empty"
                                 style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, #141e30 0%, #243b55 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 10;">
                                <div style="text-align: center; width: 100%; max-width: 500px; padding: 30px;">
                                    <div style="width: 100px; height: 100px; margin: 0 auto 30px; position: relative;">
                                        <div class="spinner-grow text-primary" role="status"
                                             style="width: 100px; height: 100px; position: absolute; top: 0; left: 0; opacity: 0.2; animation: spinner-grow 2s ease-in-out infinite alternate;"></div>
                                        <div class="spinner-grow text-primary" role="status"
                                             style="width: 80px; height: 80px; position: absolute; top: 10px; left: 10px; opacity: 0.4; animation: spinner-grow 2s ease-in-out 0.3s infinite alternate;"></div>
                                        <div class="spinner-grow text-primary" role="status"
                                             style="width: 60px; height: 60px; position: absolute; top: 20px; left: 20px; opacity: 0.6; animation: spinner-grow 2s ease-in-out 0.6s infinite alternate;"></div>
                                        <div class="spinner-grow text-primary" role="status"
                                             style="width: 40px; height: 40px; position: absolute; top: 30px; left: 30px; opacity: 0.8; animation: spinner-grow 2s ease-in-out 0.9s infinite alternate;"></div>
                                    </div>
                                    <h2 style="font-size: 35px; margin-bottom: 1.5rem; color: white; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                                        Buổi webinar đã kết
                                        thúc</h2>
                                    <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); margin-bottom: 20px; line-height: 1.6;">
                                        Cảm ơn bạn đã tham gia
                                        cùng chúng tôi.</p>
                                    <div
                                        style="width: 100%; max-width: 300px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px; margin: 15px auto; overflow: hidden;">
                                        <div
                                            style="height: 100%; width: 30%; background: var(--primary-color); border-radius: 3px; animation: progress 2s ease-in-out infinite;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                @else
                    <div class="position-relative" style="height: 100%; width: 100%;">
                        <div id="loading-container-default"
                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, #141e30 0%, #243b55 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; z-index: 10;">
                            <div style="text-align: center; width: 100%; max-width: 500px; padding: 30px;">
                                <div style="width: 100px; height: 100px; margin: 0 auto 30px; position: relative;">
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 100px; height: 100px; position: absolute; top: 0; left: 0; opacity: 0.2; animation: spinner-grow 2s ease-in-out infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 80px; height: 80px; position: absolute; top: 10px; left: 10px; opacity: 0.4; animation: spinner-grow 2s ease-in-out 0.3s infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 60px; height: 60px; position: absolute; top: 20px; left: 20px; opacity: 0.6; animation: spinner-grow 2s ease-in-out 0.6s infinite alternate;"></div>
                                    <div class="spinner-grow text-primary" role="status"
                                         style="width: 40px; height: 40px; position: absolute; top: 30px; left: 30px; opacity: 0.8; animation: spinner-grow 2s ease-in-out 0.9s infinite alternate;"></div>
                                </div>
                                @if($startTimeWebinar)
                                    <h2 style="font-size: 35px; margin-bottom: 1.5rem; color: white; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                                        Buổi webinar sắp phát sóng</h2>
                                    <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); margin-bottom: 20px; line-height: 1.6;">
                                        Thời gian phát sóng vào lúc: {{$startTimeWebinar->format("H:i d/m/Y")}}</p>
                                @else
                                    <h2 style="font-size: 35px; margin-bottom: 1.5rem; color: white; font-weight: 600; text-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                                        Buổi webinar đã kết
                                        thúc</h2>
                                    <p style="font-size: 1.1rem; color: rgba(255,255,255,0.8); margin-bottom: 20px; line-height: 1.6;">
                                        Cảm ơn bạn đã tham gia
                                        cùng chúng tôi.</p>
                                @endif
                                <div
                                    style="width: 100%; max-width: 300px; height: 6px; background: rgba(255,255,255,0.1); border-radius: 3px; margin: 15px auto; overflow: hidden;">
                                    <div
                                        style="height: 100%; width: 30%; background: var(--primary-color); border-radius: 3px; animation: progress 2s ease-in-out infinite;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>


            <div class="webinar-info">
                <div class="flex !p-3 flex-col md:flex-row">
                    <div class="w-full md:w-1/2">
                        <h3 class="webinar-title !text-xl md:!text-2xl font-bold">{{ $webinar->title }}</h3>
                        <div class="speaker flex items-center gap-2">
                            <span><i class="fas fa-microphone text-base"></i></span>
                            <span
                                class="host-name">Host: {{ $webinar->speaker ?? 'Không có thông tin diễn giả' }}</span>
                        </div>
                        @if($isLiveStream)
                            <div class="webinar-status-alert live-alert">
                                <i class="fas fa-circle"></i>
                                Buổi webinar đang diễn ra
                            </div>

                        @elseif(isset($inWaitingPeriod) && $inWaitingPeriod)
                            <div class="webinar-status-alert waiting-alert">
                                <i class="fas fa-clock"></i>
                                Buổi webinar sắp bắt đầu lúc {{ $formattedStartTime ?? '' }}
                            </div>
                        @elseif($webinar->video_path)
                            <div class="webinar-status-alert recording-alert">
                                <i class="fas fa-play-circle"></i> Bạn đang xem bản ghi của buổi webinar
                            </div>
                        @endif
                    </div>
                    <div class="w-full md:w-1/2 flex md:justify-end">
                        <div class="flex flex-col text-gray-500 mt-2 small w-fit text-left !gap-2">
                            <div><i class="fas fa-calendar-alt text-blue-500"></i> Ngày phát
                                hành: {{ \Carbon\Carbon::parse($webinar->start_date)->format('d/m/Y') }}</div>
                            <div><i class="fas fa-clock text-blue-500"></i> Thời
                                gian: {{ \Carbon\Carbon::parse($webinar->start_date)->format('H:i') }}
                                - {{ \Carbon\Carbon::parse($webinar->end_date)->format('H:i') }}</div>
                            @if($isLiveSimulate)
                                <div><i class="fas fa-chart-line text-blue-500"></i> Tiến độ: <span id="video-progress">00:00:00</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="discussion-section flex justify-between !p-4 md:hidden">
                    <button id="share-btn" class="share-btn">
                        <i class="fas fa-share-alt"></i> Chia sẻ
                    </button>
                    <button id="discussion-btn" class="discussion-btn bg-blue-500/30 text-sm md:text-base">
                        <i class="fas fa-close text-red-500"></i> Đóng trò chuyện
                    </button>
                </div>
            </div>
        </div>

        <div class="chat-section" id="chat-section">


            <div class="chat-header">
                <div class="flex items-center !p-3 md:p-4 w-full justify-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-comments"></i>
                        <h4>Trò Chuyện</h4>
                    </div>
                    <div class="chat-options block md:hidden" id="close-chat-btn">
                        <i class="fas fa-close"></i>
                    </div>
                    <div class="discussion-section hidden md:flex">
                        <button id="share-btn" class="share-btn !bg-blue-500/20 !text-blue-500">
                            <i class="fas fa-share-alt !text-blue-500"></i> Chia sẻ
                        </button>
                    </div>
                </div>
                <div class="top-navbar flex-col md:flex-row !px-4 bg-neutral-500/10">
                    <div class="flex items-center !gap-2">

                        @if((isset($is_live_stream) && $is_live_stream) || (isset($is_live_learning) && $is_live_learning))
                            <div class="live-badge" id="liveStatus">
                                <i class="fas fa-circle"></i> LIVE
                            </div>
                        @elseif(isset($isLive) && $isLive)
                            <div class="live-badge" id="liveStatus">
                                <i class="fas fa-circle"></i> LIVE
                            </div>

                            @if($isLiveSimulate)
                                <div class="viewers-count">
                                    <i class="fas fa-eye"></i>
                                    <span id="viewerCount" class="">0</span> <span
                                        class="ms-1">người xem</span>
                                </div>
                            @endif
                        @elseif($webinar->video_path)
                            <div class="recording-badge" id="liveStatus">BẢN GHI</div>
                        @endif
                        <div class="webinar-id">
                            ID: {{$webinar->join_code}}
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="messages-wrapper pt-3" style="display: flex; flex-direction: column; min-height: 100%;">
                    <!-- <div class="system-message">
                        Chào mừng đến với webinar! Hãy tham gia trò chuyện với diễn giả và những người tham gia khác.
                    </div> -->

                    <!-- Generated seeded comments if available -->
                @if(isset($seededComments) && count($seededComments) > 0)
                    <!-- Seeded comments will be displayed dynamically by JavaScript based on video timestamps -->
                        <div id="seeded-comments-data" style="display: none;"
                             data-seeded-comments="{{ json_encode($seededComments) }}"></div>
                @endif
                <!-- Real comments -->
                    @if(isset($comments) && count($comments) > 0)
                        @if($webinar->only_show_my_comment)
                            @foreach($comments as $comment)
                                @if($comment["participant"]["id"]==null || $comment["participant"]["id"] == $participantId)
                                    <div class="chat-message" data-id="{{ $comment['id'] }}">
                                        <div class="meta">
                                    <span class="user-badge">
                                        <i class="fas fa-user"></i>
                                    </span>
                                            <div class="name-time">
                                        <span class="name">
                                            {{ $comment['participant']['name'] }}
                                        </span>
                                                <span class="time">
                                                {{ $comment['created_at'] }}
                                                    @if(isset($comment['video_timestamp']) && $comment['video_timestamp'] && request()->has('debug'))
                                                        <small>(tại {{ gmdate('H:i:s', $comment['video_timestamp']) }})</small>
                                                    @endif
                                        </span>
                                            </div>
                                        </div>
                                        <div class="content">{{ $comment['content'] }}</div>
                                    </div>
                                @endif
                            @endforeach
                        @else
                            @foreach($comments as $comment)
                                <div class="chat-message" data-id="{{ $comment['id'] }}">
                                    <div class="meta">
                                    <span class="user-badge">
                                        <i class="fas fa-user"></i>
                                    </span>
                                        <div class="name-time">
                                        <span class="name">
                                            {{ $comment['participant']['name'] }}
                                        </span>
                                            <span class="time">
                                                {{ $comment['created_at'] }}
                                                @if(isset($comment['video_timestamp']) && $comment['video_timestamp'] && request()->has('debug'))
                                                    <small>(tại {{ gmdate('H:i:s', $comment['video_timestamp']) }})</small>
                                                @endif
                                        </span>
                                        </div>
                                    </div>
                                    <div class="content">{{ $comment['content'] }}</div>
                                </div>
                            @endforeach
                        @endif
                    @endif

                    <div id="new-comment-container"></div>

                </div>
            </div>
            <!-- Fixed join notification container at the bottom -->
            <div id="join-notification-container" class="join-notification-fixed"
                 style="display: none; margin-top: auto;">
                <!-- Join notifications will appear here -->
            </div>

            <div class="chat-input-container">
                <!-- Remove the join notification container from here -->
                <form id="comment-form" class="chat-input-form flex flex-row gap-2 items-center">
                    <input type="text" class="form-control" id="comment-input" style="resize: none;"
                           placeholder="Nhập tin nhắn của bạn..."/>
                    <div class="flex justify-end">
                        <button type="submit" class="btn btn-primary" id="comment-submit-btn">
                            <span class="btn-text"><i class="fas fa-paper-plane"></i></span>
                            <span class="btn-loading d-none">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<div class="noti-seeding" id="noti-seeding"></div>
<div id="popupOverlay" class="popup-overlay"></div>

{{-- Modal question --}}
<div
    id="reopenQuestionModal"
    class="fixed bottom-4 left-4 z-30"
    style="display: none"
>
    <div class="text-base font-semibold">
        Thời gian trả lời còn lại:
    </div>
    <div id="questionCountdown" class="text-lg font-bold">
        00:00
    </div>
</div>
<div id="questionPopup" class="w-[600px] bg-[#0B121F]">
    <div
    class="flex justify-between items-center border-b border-gray-700 w-full relative"
    >
    <h2
        class="text-xl p-3 text-center font-bold text-gray-100 modal-title w-full"
    >
        Tương tác
    </h2>
    <button
        id="closePopupButton"
        class="text-gray-400 hover:text-gray-200 transition-colors absolute right-2 top-2"
    >
        <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-7 w-7"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        stroke-width="2"
        >
        <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M6 18L18 6M6 6l12 12"
        />
        </svg>
    </button>
    </div>

    <div class="p-3 interaction-modal-content">Đang tải...</div>
</div>


{{-- reopen modal ads --}}
<div
    id="reopenOffer"
    class="fixed bottom-4 left-4 z-30"
    style="display: none"
>

    <div id="reopenPopupButton"
         class="cursor-pointer animate-[attentionBounce_1.5s_ease-in-out_infinite] fixed bottom-4 left-4 z-50 bg-gradient-to-r from-purple-600 to-red-500 text-white px-3 py-2 rounded-xl shadow-lg flex items-center space-x-2 animate-fade-in">
        <svg class="w-6 h-6 text-yellow-400 animate-[flashPulse_1s_ease-in-out_infinite]" fill="currentColor"
             viewBox="0 0 20 20">
            <path d="M11 0L3 10h5v10l8-10h-5L11 0z"/>
        </svg>
        <div class="text-base font-semibold">
            Ưu đãi chỉ còn
        </div>
        <div id="adCountdown" class="text-lg font-bold">
            00:00
        </div>
    </div>

</div>

{{-- offter modal --}}
<div id="offerPopup" class="w-[600px] bg-[#0B121F]">
    <div
        class="flex justify-between items-center border-b border-gray-700 w-full relative"
    >
        <h2
            class="text-xl p-3 text-center font-bold text-gray-100 modal-title w-full"
        >
            ƯU ĐÃI CHƯƠNG TRÌNH
        </h2>
        <button
            class="closePopupButton text-gray-400 hover:text-gray-200 transition-colors absolute right-2 top-2"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-7 w-7"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                />
            </svg>
        </button>
    </div>

    <div class="p-3">
        <div class="registerForm">
            <div id="productInfo" class="bg-[#1F2937] p-3 rounded-xl mb-6">
                <div id="" class="product-type-ad flex flex-col md:flex-row gap-3 md:gap-4">
                    <div
                        class="w-full md:w-1/4 rounded-lg flex items-center justify-center text-gray-300 text-sm"
                    >
                        <img
                            src="https://placehold.co/600x400/EEE/31343C"
                            alt="Sản phẩm"
                            class="w-full h-[180px] object-cover rounded-lg modal-image-product"
                        />
                    </div>
                    <div class="w-full flex-1">
                        <h3
                            class="text-base font-semibold text-white mb-2 modal-description"
                        >
                            Combo 3 cuốn sách Marketing chiến lược
                        </h3>
                        <p
                            class="text-sm text-gray-300 mb-3 leading-relaxed modal-subdescription"
                        >
                            Bộ sách cung cấp đầy đủ kiến thức Marketing từ A-Z dành cho
                            doanh nghiệp SMEs, hỗ trợ cải thiện chiến lược tăng doanh thu
                        </p>
                        <div
                            class="flex flex-wrap items-center gap-2 mb-3 border-y border-gray-700 py-2"
                        >
                  <span
                      class="text-lg md:text-xl font-bold text-[#0072DE] modal-price"
                  >699.000 ₫</span
                  >
                            <span
                                class="text-base text-gray-400 line-through modal-old-price"
                            >1.200.000 ₫</span
                            >
                            <span
                                class="bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded-full modal-discount"
                            >Giảm 40%</span
                            >
                        </div>
                        <div class="flex flex-col md:flex-row items-center text-sm text-gray-400">
                            <span class="text-green-400">
                                <i class="fas fa-shopping-cart mr-1"></i>
                                <span class="modal-total-sold">50 người đã mua</span>
                            </span>
                            <span class="mx-2 hidden md:block">|</span>
                            <span class="modal-remaining text-white">
                                Còn lại 20 sản phẩm
                            </span>
                        </div>
                    </div>
                </div>
                <div class="image-type-ad">
                    <img
                        src=""
                        alt="Sản phẩm"
                        class="w-full h-[210px] object-cover rounded-lg modal-image-img"
                    />
                </div>
            </div>

            <form class="main-form" action="#" method="POST">
                <div class="!mb-5 regis-form-field">
                    <label
                        for="fullName"
                        class="block text-sm font-medium text-gray-300 mb-1"
                    >Họ và tên <span class="text-red-500">*</span></label
                    >
                    <input
                        type="text"
                        name="fullName"
                        id="fullName"
                        value="{{ $participant->name }}"
                        placeholder="Nhập thông tin"
                        class="fullName w-full bg-gray-700 border border-gray-600 text-gray-200 placeholder-gray-400 px-4 py-3 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                    />
                </div>
                <div class="!mb-6 regis-form-field">
                    <label
                        for="phoneNumber"
                        class="block text-sm font-medium text-gray-300 mb-1"
                    >Số điện thoại <span class="text-red-500">*</span></label
                    >
                    <input
                        type="tel"
                        name="phoneNumber"
                        id="phoneNumber"
                        value="{{ $participant->phone }}"
                        placeholder="Nhập thông tin"
                        class="phoneNumber w-full bg-gray-700 border border-gray-600 text-gray-200 placeholder-gray-400 px-4 py-3 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                    />
                </div>

                <div class="flex justify-center">
                    <button
                        style="display: block"
                        type="submit"
                        class="submitFormButton w-full bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md transition-colors duration-150 ease-in-out text-sm md:text-base"
                    >
                        Nhận tư vấn
                    </button>
                </div>
            </form>
            <a
                href="#"
                target="_blank"
                style="display: none"
                class="redirectUrlButton w-full block text-center bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md transition-colors duration-150 ease-in-out text-sm md:text-base"
            >
                Truy cập link
            </a>
        </div>
        <div class="modal-regis paymentForm">
            <div class="payment-methods-container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="method-option">
                            <div class="method-title">
                                <strong>Cách 1:</strong> Mở app ngân hàng/Ví và
                                <strong>Quét mã QR</strong>
                            </div>
                            <div class="qr-container">
                                <img
                                    src="https://elearning.topid.vn/assets/frontend/course-shopee/assets/images/border-bank.png"
                                    alt="Border Bank"
                                    class="border-bank img-fluid w-full mb-2"
                                />

                                <div
                                    id="qrcode"
                                    class="img-fluid mb-2 img-bank"
                                    title="00020101021238520010A000000727012200069704480108SEPTTKQA0208QRIBFTTA53037045405150005802VN62160812X5H85WBCQFTA6304378C"
                                    style="cursor: pointer"
                                >
                                    <div class="scanner"></div>
                                    <canvas
                                        width="200"
                                        height="200"
                                        style="display: none"
                                    ></canvas
                                    >
                                    <img
                                        class="img-qr-code"
                                        style="display: block"
                                        src=""
                                    />
                                </div>
                                <button class="btn btn-primary download-qr-btn mt-2 w-100">
                                    <i class="fas fa-download"></i> Tải xuống mã QR
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="method-option">
                            <div class="method-title">
                                <strong>Cách 2:</strong> Chuyển khoản
                                <strong>thủ công</strong> theo thông tin
                            </div>
                            <div class="body-method-option">
                                <div class="bank-info">
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Thụ hưởng:</span>
                                        <span class="bank-info-value">{{ setting('sepay_merchant_name') }}</span>
                                    </div>
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Số tài khoản:</span>
                                        <span class="bank-info-value"
                                        >{{setting('sepay_account_number')}}
                          <i
                              class="bi bi-copy copy-value text-black"
                          ></i
                          ></span>
                                    </div>
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Số tiền:</span>
                                        <span class="bank-info-value bank_total_amount"
                                        >0₫</span
                                        >
                                    </div>
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Nội dung CK:</span>
                                        <div>
                          <span class="bank-info-value bank_transaction_content"
                          >...</span
                          >
                                            <i
                                                class="bi bi-copy copy-value text-black"
                                                data-value=""
                                            ></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="warning-box">
                                    <i
                                        class="bi bi-exclamation-triangle-fill warning-icon"
                                    ></i>
                                    <span class="fw-bold">Lưu ý:</span>
                                    Vui lòng giữ nguyên nội dung chuyển khoản
                                    <strong class="bank_transaction_content"
                                    >X5H85WBCQFTA</strong
                                    >
                                    để xác nhận thanh toán tự động.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timer-container">
                    <div>Đơn hàng sẽ bị hủy sau:</div>
                    <div class="timer">00:14:59</div>
                </div>

                <div class="status-container">
                    <strong>Trạng thái:</strong> Chờ thanh toán
                    <span class="loading-icon">
                <div class="custom-spinner"></div>
              </span>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- advertisment real time modal --}}
<div id="adRealTimePopup" class="w-[600px] bg-[#0B121F]">
    <div
        class="flex justify-between items-center border-b border-gray-700 w-full relative"
    >
        <h2
            class="text-xl p-3 text-center font-bold text-gray-100 modal-title w-full"
        >
            ƯU ĐÃI CHƯƠNG TRÌNH
        </h2>
        <button
            class="closePopupButton text-gray-400 hover:text-gray-200 transition-colors absolute right-2 top-2"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-7 w-7"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                stroke-width="2"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                />
            </svg>
        </button>
    </div>

    <div class="p-3">
        <div class="registerForm">
            <div id="productInfo" class="bg-[#1F2937] p-3 rounded-xl mb-6">
                <div id="" class="product-type-ad flex flex-col md:flex-row gap-3 md:gap-4">
                    <div
                        class="w-full md:w-1/4 rounded-lg flex items-center justify-center text-gray-300 text-sm"
                    >
                        <img
                            src="https://placehold.co/600x400/EEE/31343C"
                            alt="Sản phẩm"
                            class="w-full h-[180px] object-cover rounded-lg modal-image-product"
                        />
                    </div>
                    <div class="w-full flex-1">
                        <h3
                            class="text-base font-semibold text-white mb-2 modal-description"
                        >
                            Combo 3 cuốn sách Marketing chiến lược
                        </h3>
                        <p
                            class="text-sm text-gray-300 mb-3 leading-relaxed modal-subdescription"
                        >
                            Bộ sách cung cấp đầy đủ kiến thức Marketing từ A-Z dành cho
                            doanh nghiệp SMEs, hỗ trợ cải thiện chiến lược tăng doanh thu
                        </p>
                        <div
                            class="flex flex-wrap items-center gap-2 mb-3 border-y border-gray-700 py-2"
                        >
                  <span
                      class="text-lg md:text-xl font-bold text-[#0072DE] modal-price"
                  >699.000 ₫</span
                  >
                            <span
                                class="text-base text-gray-400 line-through modal-old-price"
                            >1.200.000 ₫</span
                            >
                            <span
                                class="bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded-full modal-discount"
                            >Giảm 40%</span
                            >
                        </div>
                        <div class="flex flex-col md:flex-row items-center text-sm text-gray-400">
                            <span class="text-green-400">
                                <i class="fas fa-shopping-cart mr-1"></i>
                                <span class="modal-total-sold">50 người đã mua</span>
                            </span>
                            <span class="mx-2 hidden md:block">|</span>
                            <span class="modal-remaining text-white">
                                Còn lại 20 sản phẩm
                            </span>
                        </div>
                    </div>
                </div>
                <div class="image-type-ad">
                    <img
                        src=""
                        alt="Sản phẩm"
                        class="w-full h-[210px] object-cover rounded-lg modal-image-img"
                    />
                </div>
            </div>

            <form class="main-form" action="#" method="POST">
                <div class="!mb-5 regis-form-field">
                    <label
                        for="fullName"
                        class="block text-sm font-medium text-gray-300 mb-1"
                    >Họ và tên <span class="text-red-500">*</span></label
                    >
                    <input
                        type="text"
                        name="fullName"
                        id="fullName"
                        value="{{ $participant->name }}"
                        placeholder="Nhập thông tin"
                        class="fullName w-full bg-gray-700 border border-gray-600 text-gray-200 placeholder-gray-400 px-4 py-3 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                    />
                </div>
                <div class="!mb-6 regis-form-field">
                    <label
                        for="phoneNumber"
                        class="block text-sm font-medium text-gray-300 mb-1"
                    >Số điện thoại <span class="text-red-500">*</span></label
                    >
                    <input
                        type="tel"
                        name="phoneNumber"
                        id="phoneNumber"
                        value="{{ $participant->phone }}"
                        placeholder="Nhập thông tin"
                        class="phoneNumber w-full bg-gray-700 border border-gray-600 text-gray-200 placeholder-gray-400 px-4 py-3 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
                    />
                </div>

                <div class="flex justify-center">
                    <button
                        style="display: block"
                        type="submit"
                        class="submitFormButton w-full bg-blue-600  text-white font-semibold py-3 px-4 rounded-lg shadow-md transition-colors duration-150 ease-in-out text-sm md:text-base"
                    >
                        Nhận tư vấn
                    </button>
                </div>
            </form>
            <a
                href="#"
                target="_blank"
                style="display: none"
                class="redirectUrlButton w-full block text-center bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md transition-colors duration-150 ease-in-out text-sm md:text-base"
            >
                Truy cập link
            </a>
        </div>
        <div class="modal-regis paymentForm">
            <div class="payment-methods-container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="method-option">
                            <div class="method-title">
                                <strong>Cách 1:</strong> Mở app ngân hàng/Ví và
                                <strong>Quét mã QR</strong>
                            </div>
                            <div class="qr-container">
                                <img
                                    src="https://elearning.topid.vn/assets/frontend/course-shopee/assets/images/border-bank.png"
                                    alt="Border Bank"
                                    class="border-bank img-fluid w-full mb-2"
                                />

                                <div
                                    id="qrcode"
                                    class="img-fluid mb-2 img-bank"
                                    title="00020101021238520010A000000727012200069704480108SEPTTKQA0208QRIBFTTA53037045405150005802VN62160812X5H85WBCQFTA6304378C"
                                    style="cursor: pointer"
                                >
                                    <div class="scanner"></div>
                                    <canvas
                                        width="200"
                                        height="200"
                                        style="display: none"
                                    ></canvas
                                    >
                                    <img
                                        class="img-qr-code"
                                        style="display: block"
                                        src=""
                                    />
                                </div>
                                <button class="btn btn-primary download-qr-btn mt-2 w-100">
                                    <i class="fas fa-download"></i> Tải xuống mã QR
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="method-option">
                            <div class="method-title">
                                <strong>Cách 2:</strong> Chuyển khoản
                                <strong>thủ công</strong> theo thông tin
                            </div>
                            <div class="body-method-option">
                                <div class="bank-info">
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Thụ hưởng:</span>
                                        <span class="bank-info-value">{{ setting('sepay_merchant_name') }}</span>
                                    </div>
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Số tài khoản:</span>
                                        <span class="bank-info-value"
                                        >{{setting('sepay_account_number')}}
                          <i
                              class="bi bi-copy copy-value text-black"
                          ></i
                          ></span>
                                    </div>
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Số tiền:</span>
                                        <span class="bank-info-value bank_total_amount"
                                        >0₫</span
                                        >
                                    </div>
                                    <div class="bank-info-row">
                                        <span class="bank-info-label">Nội dung CK:</span>
                                        <div>
                          <span class="bank-info-value bank_transaction_content"
                          >...</span
                          >
                                            <i
                                                class="bi bi-copy copy-value text-black"
                                                data-value=""
                                            ></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="warning-box">
                                    <i
                                        class="bi bi-exclamation-triangle-fill warning-icon"
                                    ></i>
                                    <span class="fw-bold">Lưu ý:</span>
                                    Vui lòng giữ nguyên nội dung chuyển khoản
                                    <strong class="bank_transaction_content"
                                    >X5H85WBCQFTA</strong
                                    >
                                    để xác nhận thanh toán tự động.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timer-container">
                    <div>Đơn hàng sẽ bị hủy sau:</div>
                    <div class="timer">00:14:59</div>
                </div>

                <div class="status-container">
                    <strong>Trạng thái:</strong> Chờ thanh toán
                    <span class="loading-icon">
                <div class="custom-spinner"></div>
              </span>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Share dialog -->
<div class="share-dialog-backdrop" id="share-backdrop"></div>
<div class="share-dialog" id="share-dialog">
    <h4>
        Chia sẻ webinar này
        <button class="close-btn" id="close-share"><i class="fas fa-times"></i></button>
    </h4>
    <div class="share-url">
        <input type="text" id="share-url" value="{{ url('/join/'.$webinar->join_code) }}" readonly>
        <button id="copy-link"><i class="fas fa-copy"></i></button>
    </div>
    <p class="text-secondary small">Chia sẻ link qua mạng xã hội</p>
    <div class="share-options">
        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url('/join/'.$webinar->join_code)) }}"
           target="_blank" class="share-option facebook">
            <div class="icon"><i class="fab fa-facebook-f"></i></div>
            <span>Facebook</span>
        </a>
        <a href="https://twitter.com/intent/tweet?url={{ urlencode(url('/join/'.$webinar->join_code)) }}&text={{ urlencode($webinar->title) }}"
           target="_blank" class="share-option twitter">
            <div class="icon"><i class="fab fa-twitter"></i></div>
            <span>Twitter</span>
        </a>
        <a href="mailto:?subject={{ urlencode($webinar->title) }}&body={{ urlencode('Tham gia webinar cùng tôi tại: '.url('/join/'.$webinar->join_code)) }}"
           class="share-option email">
            <div class="icon"><i class="fas fa-envelope"></i></div>
            <span>Email</span>
        </a>
        <a href="https://wa.me/?text={{ urlencode($webinar->title.': '.url('/join/'.$webinar->join_code)) }}"
           target="_blank" class="share-option whatsapp">
            <div class="icon"><i class="fab fa-whatsapp"></i></div>
            <span>WhatsApp</span>
        </a>
    </div>
</div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>

<!-- Video.js -->
<script src="https://vjs.zencdn.net/8.3.0/video.min.js"></script>

<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- webinar -->
<script src="https://unpkg.com/axios@1.6.7/dist/axios.min.js"></script>

<?php $testMod = 0; ?>
<!-- <script src="{{ asset('jstest/js/common/modal-util.js') }}"></script>
<script src="{{ asset('jstest/js/webinar/seeding-util.js') }}"></script>
<script src="{{ asset('jstest/js/webinar/advertisement-manager.js') }}"></script>
<script src="{{ asset('jstest/js/common/webinar-common.js') }}"></script>
<script src="{{ asset('jstest/js/elearning/template-loader.js') }}"></script>
<script src="{{ asset('jstest/js/elearning/interaction-modal.js') }}"></script>
<script src="{{ asset('jstest/js/webinar/video-manager.js') }}"></script>
<script src="{{ asset('jstest/js/webinar/chat-manager.js') }}"></script> -->

@if(!$testMod)
@vite([
    'resources/js/common/modal-util.js',
    'resources/js/webinar/seeding-util.js',
    'resources/js/webinar/advertisement-manager.js',
    'resources/js/common/webinar-common.js'
])

@vite([
    'resources/js/elearning/template-loader.js',
    'resources/js/elearning/interaction-modal.js',
])

@vite([
'resources/js/webinar/video-manager.js',
'resources/js/webinar/chat-manager.js'
])
@endif



<script>

    var webinarConfig = {
        sepay_merchant_name: "{{setting('sepay_merchant_name')}}",
        sepay_account_number: "{{setting('sepay_account_number')}}",
        sepay_bank_code: "{{setting('sepay_bank_code')}}",
        webinarStartTime: new Date('{{ $webinar->start_date ?? date("Y-m-d H:i:s") }}'),
        isLivestream: !!{{ $isLiveStream ? 'true' : 'false' }},
        isLiveReal: !!{{ $isLiveReal ? 'true' : 'false' }},
        isLiveSimulate: !!{{ $isLiveSimulate ? 'true' : 'false' }},
        isLiveElearing: !!{{ $is_live_learning? 'true' : 'false' }},
        isRecorded: !!{{ isset($isRecorded) && $isRecorded ? 'true' : 'false' }},
        advertisements: {!! json_encode($advertisements) !!},
        videoTimestamp: {{ $videoTimestamp ?? 0 }},
        isDebug: {{ session("debug") === '1' ? 'true' : 'false' }},
        inWaitingPeriod: {{ isset($inWaitingPeriod) && $inWaitingPeriod ? 'true' : 'false' }},
        waitingTimeRemaining: {{ $waitingTimeRemaining ?? 0 }},
        participantId: '{{ $participant->id ?? "" }}',
        webinarId: '{{ $webinar->join_code }}', // Added for consistency
        webinarRawStartDate: '{{ $webinar->start_date ?? date("Y-m-d H:i:s") }}',
        webinarRawEndDate: '{{ $webinar->end_date ?? date("Y-m-d H:i:s", strtotime("+1 hour")) }}',
        typeLive: '{{ $type_live }}', // 1: youtube, 2: jitsi, 3: facebook, 4: m3u8
    }
    if (window.location.search.includes('?debug')) {
        console.log("webinarConfig", webinarConfig);
    }

    var webinarInfo = {!! json_encode($webinar) !!};
    var apiEndpoint = {
        checkout: "{{ route('checkout.payment') }}",
        verifyPayment: "{{ route('check.payment') }}",
        fetchComments: "{{ route('join.comments', $webinar->join_code) }}",
        postComment: "{{ route('join.comment', $webinar->join_code) }}",
        updateDuration: "{{ route('join.update-duration', $webinar->join_code) }}",
        getLiveAd: "{{route('join.get.live',$webinar->join_code)}}",
        submitQuestion: function(questionId) {
            return "{{route('join.question.responses', ['code'=>$webinar->join_code, 'question_id' => '__QUESTION_ID__'])}}".replace('__QUESTION_ID__', questionId);
        }
    }
    console.log(apiEndpoint)


    function formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // Format time with hours helper function
    function formatTimeWithHours(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    function startVideoTimeLogging(cb) {
        const $liveVideoContainer = $('#native-live-video');
        const $regularVideoContainer = $('#native-video');

        // Function to get video element from container
        function getVideoElement($container) {
            if ($container.length === 0) return null;

            // Check if the container itself is a video element
            if ($container.is('video')) {
                return $container[0];
            }

            // If not, look for a video element inside the container
            const $videoInside = $container.find('video');
            if ($videoInside.length > 0) {
                return $videoInside[0];
            }

            return null;
        }

        const liveVideoElement = getVideoElement($liveVideoContainer);
        const regularVideoElement = getVideoElement($regularVideoContainer);

        const logVideoTime = () => {
            if (liveVideoElement && !liveVideoElement.paused) {
                const time = liveVideoElement.currentTime;
                const hours = Math.floor(time / 3600);
                const minutes = Math.floor((time % 3600) / 60);
                const seconds = Math.floor(time % 60);
                const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                $('#video-progress').text(formattedTime);
                cb(formattedTime);

            } else if (regularVideoElement && !regularVideoElement.paused) {
                console.log(`Recorded video current time: ${regularVideoElement.currentTime.toFixed(2)} seconds`);
            }
        };

        // Log every second
        const loggingInterval = setInterval(logVideoTime, 1000);

        // Stop logging if both videos are not available
        if (!liveVideoElement && !regularVideoElement) {
            console.log('No video elements found, stopping time logging');
            clearInterval(loggingInterval);
        }

        // Return the interval ID in case you want to clear it later
        return loggingInterval;
    }
</script>


<script>
    $(document).ready(function () {
        if (webinarConfig.isLiveSimulate && !webinarConfig.isLiveElearing) {
            // Initialize the Advertisement Manager
            const adManager = new AdvertisementManager('#offerPopup', webinarConfig.advertisements, webinarInfo, {
                seedingInterval: {min: 5, max: 10},
                paymentCheckInterval: 5000,
                paymentTimeout: 15 * 60, // 15 minutes in seconds
                showSeeding: true,
            });

            startVideoTimeLogging(function (time) {
                adManager.checkForAdvertisements(time);
            });
        }

        if (webinarConfig.isLiveReal && !webinarConfig.isLiveElearing) {
            const realTimeAdManager = new ManualAdvertisementManager("#adRealTimePopup", webinarInfo, {
                seedingInterval: {min: 5, max: 10},
                paymentCheckInterval: 5000,
                paymentTimeout: 15 * 60, // 15 minutes in seconds
                isManualMode: true
            })

            // Function to fetch live advertisement data recursively
            function fetchLiveAdvertisement() {
                axios.get(apiEndpoint.getLiveAd)
                    .then(response => {
                        const results = response.data;

                        if (!results.error) {
                            const data = results.data;
                            realTimeAdManager.showManualAdvertisement(data.advertisements, data.default_code);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching live ad:', error);
                    })
                    .finally(() => {
                        setTimeout(fetchLiveAdvertisement, 3000);
                    })
            }

            // Start the recursive function
            // fetchLiveAdvertisement();
        }

        if (webinarConfig.isLiveElearing) {
          const interactionSystem = new window.InteractionModal();

            const configs = {
                "single-choice": {
                    icon: '❓'
                },
                "true-false": {
                    icon: '⚖️'
                },
                "open-survey": {
                    icon: '💭'
                },
                "multiple-choice": {
                    icon: '✅'
                },
            }

            // Function to fetch live advertisement data recursively
            function fetchLiveQuestion() {
                axios.get(apiEndpoint.getLiveAd)
                    .then(response => {
                        const results = response.data;
                        if(!results || !results?.data?.questions?.question){
                            return interactionSystem.onClose()
                        }

                        if (!results.error) {
                            const data = results.data;
                            const formatData = {
                                ...data?.questions,
                                title: data?.questions?.description,
                                description: '',
                                icon: configs[data?.questions?.type]?.icon,
                                options: data?.questions?.options?.map(e => ({
                                    ...e,
                                    id: e?.value,
                                })),
                                showIn: 2000,
                            }
                            interactionSystem.showModalDirectly(formatData, data.default_code);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching live question:', error);
                    })
                    .finally(() => {
                        setTimeout(fetchLiveQuestion, 3000);
                    })
            }

            // Start the recursive function
            fetchLiveQuestion();
        }

    });
</script>

<script>
    $(document).ready(function () {
        var isChatOpen = true;
        $('#close-chat-btn').click(function () {
            toggleChat();
        });
        $('#discussion-btn').click(function () {
            toggleChat();
        });

        function toggleChat() {
            if (isChatOpen) {
                $('#chat-section').fadeOut(100);
                $('#discussion-btn').html('<i class="fas fa-comments"></i> Mở trò chuyện');
            } else {
                $('#chat-section').fadeIn(100);
                $('#discussion-btn').html('<i class="fas fa-close text-red-500"></i> Đóng trò chuyện');
            }
            isChatOpen = !isChatOpen;
        }

        $(window).resize(function () {

        });
        if (window.innerWidth < 768) {
            const offsetTop = $('.webinar-info').offset().top;
            $('#chat-section').css('--top', offsetTop + 'px');
        }
    });
</script>
</body>
</html>
